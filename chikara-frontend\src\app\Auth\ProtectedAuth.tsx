import LoadingSpinner from "@/components/Spinners/Spinner";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useMarqueeBanner from "@/hooks/useMarqueeBanner";
import useRedirects from "@/hooks/useRedirects";
import useTimestampCountdowns from "@/hooks/useTimestampCountdowns";
import * as Sentry from "@sentry/browser";
import { useQueryClient } from "@tanstack/react-query";
import posthog from "posthog-js";
import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";
import useGetCraftingQueue from "@/features/crafting/api/useGetCraftingQueue";
import { usePersistStore } from "@/app/store/stores";

const ProtectedAuth = () => {
    const queryClient = useQueryClient();
    const [analyticsIdentifier, setAnalyticsIdentifier] = useState(false);
    const { staleCurrentUserData } = usePersistStore();
    useMarqueeBanner();

    const { data, refetch, isSuccess, isLoading } = useFetchCurrentUser();
    const { data: craftingQueue } = useGetCraftingQueue({
        enabled: !!data, // Only fetch crafting queue when user data is available
    });

    useRedirects(data, isSuccess);
    useTimestampCountdowns(data, craftingQueue, refetch, queryClient);

    useEffect(() => {
        if (data && !analyticsIdentifier) {
            const distinct_id = String(data.id);
            posthog.identify(distinct_id, {
                email: data.email,
                username: data.username,
                userType: data.userType,
            });
            Sentry.setUser({ id: data.id, username: data.username });
            setAnalyticsIdentifier(true);
        }
    }, [data, analyticsIdentifier]);

    // Only show loading spinner on initial load, not during navigation
    if (!data && isLoading && !staleCurrentUserData) {
        return <LoadingSpinner />;
    }

    if (data?.banExpires && new Date(data.banExpires) > new Date()) {
        return <div className="m-auto mt-96 size-full text-center text-6xl text-red-600 md:mt-auto">Banned</div>;
    }

    return <Outlet />;
};

export default ProtectedAuth;
