import { useEffect } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import { protectedPaths } from "../app/Routes/protectedPaths";
import { checkLevelGate } from "../helpers/levelGates";
// import { useNormalStore } from "../app/store/stores";
import type { User } from "../types/user";

// Map of routes to feature keys for level gate checking
const routeToFeatureMap: Record<string, { featureKey: string; redirectPath: string }> = {
    "/market": { featureKey: "market", redirectPath: "/home" },
    "/crafting": { featureKey: "crafting", redirectPath: "/home" },
    "/workshop": { featureKey: "crafting", redirectPath: "/home" },
    "/job": { featureKey: "job", redirectPath: "/home" },
    "/talents": { featureKey: "talents", redirectPath: "/home" },
    "/courses": { featureKey: "courses", redirectPath: "/home" },
    "/arcade": { featureKey: "arcade", redirectPath: "/home" },
    "/rooftop": { featureKey: "rooftop", redirectPath: "/home" },
    "/dailyquests": { featureKey: "dailyQuests", redirectPath: "/home" },
    "/shops/1": { featureKey: "shop1", redirectPath: "/home" },
    "/shops/2": { featureKey: "shop2", redirectPath: "/home" },
    "/shops/3": { featureKey: "shop3", redirectPath: "/home" },
};

const useRedirects = (userData: User | null | undefined, isUserDataSuccess: boolean): void => {
    const navigate = useNavigate();
    const location = useLocation();
    // const { preventNavigation } = useNormalStore();

    useEffect(() => {
        if (userData?.battleValidUntil && Number.parseInt(userData?.battleValidUntil) > 0) {
            const currentRoute = location.pathname;
            const allowedRoute = "/fight";

            if (allowedRoute && currentRoute !== allowedRoute) {
                navigate(allowedRoute);
            }
        }
    }, [location.pathname, userData?.battleValidUntil, navigate]);

    useEffect(() => {
        if (userData) {
            if (userData.hospitalisedUntil && new Date(userData.hospitalisedUntil).getTime() > Date.now()) {
                if (protectedPaths.every((path) => !location.pathname.includes(path))) {
                    toast.error("You are unconscious!");
                    navigate("/hospital");
                }
            }
            if (userData.jailedUntil && new Date(userData.jailedUntil).getTime() > Date.now()) {
                if (protectedPaths.every((path) => !location.pathname.includes(path))) {
                    toast.error("You are in jail!");
                    navigate("/jail");
                }
            }
        }
    }, [location.pathname, userData?.hospitalisedUntil, userData?.jailedUntil, navigate]);

    // Originally was to keep players on classroom page
    // useEffect(() => {
    //     if (preventNavigation) {
    //         if (location.pathname !== preventNavigation) {
    //             navigate(preventNavigation);
    //         }
    //     }
    // }, [preventNavigation, location.pathname, navigate]);

    useEffect(() => {
        if (userData?.jobId && !userData?.jobPayoutHour) {
            navigate("/job");
        }
    }, [userData?.jobId, userData?.jobPayoutHour, navigate]);

    // Level gate checking for protected features
    useEffect(() => {
        if (userData) {
            const currentPath = location.pathname;
            const userLevel = userData.level || 0;

            // Check if current route requires level gate validation
            const routeMatch = Object.keys(routeToFeatureMap).find(
                (route) => currentPath === route || currentPath.startsWith(route)
            );

            if (routeMatch) {
                const { featureKey, redirectPath } = routeToFeatureMap[routeMatch];
                const { isLocked } = checkLevelGate(featureKey, userLevel);

                if (isLocked) {
                    toast.error("You cannot access this location yet");
                    navigate(redirectPath);
                }
            }
        }
    }, [location.pathname, userData?.level, navigate]);
};

export default useRedirects;
